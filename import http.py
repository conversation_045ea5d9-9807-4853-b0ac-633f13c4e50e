import http.server
import socketserver
import os

PORT = 4000
FILE_TO_SERVE = "index.html"

class CustomHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = f'/{FILE_TO_SERVE}'
        return super().do_GET()

os.chdir(os.path.dirname(os.path.abspath(__file__)))  # Set working directory to script's location
Handler = CustomHandler

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"Serving Preeklezen.html at http://localhost:{PORT}")
    httpd.serve_forever()